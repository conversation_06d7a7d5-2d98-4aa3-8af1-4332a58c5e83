#!/bin/bash

# Webtop Setup Script for PPG2 Streaming
# This script installs OBS Studio, Chromium, and configures audio for streaming

set -e

echo "Starting PPG2 Webtop setup..."

# Update package lists
apt-get update

# Install essential packages
echo "Installing essential packages..."
apt-get install -y \
    software-properties-common \
    wget \
    curl \
    gpg \
    pulseaudio \
    pulseaudio-utils \
    pavucontrol \
    alsa-utils \
    chromium-browser \
    firefox \
    git \
    vim \
    htop

# Add OBS Studio PPA and install
echo "Installing OBS Studio..."
add-apt-repository -y ppa:obsproject/obs-studio
apt-get update
apt-get install -y obs-studio

# Install additional audio tools for virtual microphone
echo "Installing audio tools..."
apt-get install -y \
    pulseaudio-module-loopback \
    pulseaudio-module-null-sink \
    pulseaudio-module-virtual-source \
    pulseaudio-module-virtual-surround-sink \
    jackd2 \
    qjackctl

# Create PulseAudio configuration for virtual audio routing
echo "Configuring PulseAudio for virtual audio routing..."
mkdir -p /config/.config/pulse

# Create a simpler configuration that loads after the default
cat > /config/.config/pulse/default.pa << 'EOF'
# Load the system default configuration first
.include /etc/pulse/default.pa

# Load virtual audio modules after a delay to ensure system is ready
.fail
EOF

# Create a separate script to load modules after PulseAudio starts
cat > /config/load-virtual-audio.sh << 'EOF'
#!/bin/bash
# Load virtual audio modules after PulseAudio is fully started

echo "Loading virtual audio modules..."
sleep 2

# Unload any existing virtual modules
pactl unload-module module-null-sink 2>/dev/null || true
pactl unload-module module-loopback 2>/dev/null || true
pactl unload-module module-virtual-source 2>/dev/null || true

sleep 1

# Load virtual speaker (desktop audio output)
VIRTUAL_SPEAKER=$(pactl load-module module-null-sink sink_name=virtual_speaker sink_properties=device.description="Virtual_Speaker")
echo "Loaded virtual speaker: module $VIRTUAL_SPEAKER"

# Load virtual microphone (for OBS capture)
VIRTUAL_MIC=$(pactl load-module module-null-sink sink_name=virtual_mic sink_properties=device.description="Virtual_Microphone")
echo "Loaded virtual microphone: module $VIRTUAL_MIC"

# Create loopback from speaker to microphone
LOOPBACK=$(pactl load-module module-loopback source=virtual_speaker.monitor sink=virtual_mic latency_msec=1)
echo "Loaded loopback: module $LOOPBACK"

sleep 1

# Set defaults
pactl set-default-sink virtual_speaker
echo "Set default sink to virtual_speaker"

echo "Virtual audio setup complete!"
EOF

chmod +x /config/load-virtual-audio.sh

# Create OBS configuration directory and basic scene
echo "Setting up OBS configuration..."
mkdir -p /config/.config/obs-studio/basic/scenes
mkdir -p /config/.config/obs-studio/basic/profiles/Untitled/basic.ini

# Create a basic OBS profile for streaming
cat > /config/.config/obs-studio/basic/profiles/Untitled/basic.ini << 'EOF'
[General]
Name=Untitled

[Video]
BaseCX=1920
BaseCY=1080
OutputCX=1280
OutputCY=720
FPSType=0
FPSCommon=30

[Audio]
SampleRate=44100
ChannelSetup=Stereo
EOF

# Create a basic scene collection
cat > /config/.config/obs-studio/basic/scenes/Untitled.json << 'EOF'
{
    "current_scene": "PPG2 Stream",
    "current_program_scene": "PPG2 Stream",
    "scene_order": [
        {
            "name": "PPG2 Stream"
        }
    ],
    "name": "Untitled",
    "sources": [
        {
            "balance": 0.5,
            "deinterlace_field_order": 0,
            "deinterlace_mode": 0,
            "enabled": true,
            "flags": 0,
            "hotkeys": {},
            "id": "browser_source",
            "mixers": 255,
            "monitoring_type": 0,
            "muted": false,
            "name": "PPG2 Frontend",
            "prev_ver": 469762048,
            "private_settings": {},
            "push-to-mute": false,
            "push-to-mute-delay": 0,
            "push-to-talk": false,
            "push-to-talk-delay": 0,
            "settings": {
                "css": "body { margin: 0px auto; overflow: hidden; }",
                "height": 720,
                "url": "http://frontend:9999",
                "width": 1280
            },
            "sync": 0,
            "versioned_id": "browser_source",
            "volume": 1.0
        },
        {
            "balance": 0.5,
            "deinterlace_field_order": 0,
            "deinterlace_mode": 0,
            "enabled": true,
            "flags": 0,
            "hotkeys": {},
            "id": "pulse_input_capture",
            "mixers": 255,
            "monitoring_type": 0,
            "muted": false,
            "name": "Desktop Audio",
            "prev_ver": 469762048,
            "private_settings": {},
            "push-to-mute": false,
            "push-to-mute-delay": 0,
            "push-to-talk": false,
            "push-to-talk-delay": 0,
            "settings": {
                "device_id": "virtual_mic_source"
            },
            "sync": 0,
            "versioned_id": "pulse_input_capture",
            "volume": 1.0
        }
    ],
    "quick_transitions": [
        {
            "duration": 300,
            "fade_to_black": false,
            "hotkeys": [],
            "id": 1,
            "name": "Cut"
        },
        {
            "duration": 300,
            "fade_to_black": false,
            "hotkeys": [],
            "id": 2,
            "name": "Fade"
        }
    ],
    "scenes": [
        {
            "hotkeys": {},
            "id": 3,
            "name": "PPG2 Stream",
            "sources": [
                {
                    "name": "PPG2 Frontend"
                },
                {
                    "name": "Desktop Audio"
                }
            ]
        }
    ],
    "transitions": [
        {
            "hotkeys": [],
            "id": "cut_transition",
            "name": "Cut"
        },
        {
            "hotkeys": [],
            "id": "fade_transition",
            "name": "Fade"
        }
    ]
}
EOF

# Create desktop shortcuts
echo "Creating desktop shortcuts..."
mkdir -p /config/Desktop

# OBS Studio shortcut
cat > /config/Desktop/OBS.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=OBS Studio
Comment=Live streaming and recording software
Exec=obs
Icon=obs
Terminal=false
Categories=AudioVideo;Video;
EOF

# Chromium shortcut for frontend with proper resolution
cat > /config/Desktop/PPG2-Frontend.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=PPG2 Frontend
Comment=Open PPG2 Frontend in Chromium at 1280x720
Exec=/config/launch-ppg2-frontend.sh
Icon=chromium-browser
Terminal=false
Categories=Network;WebBrowser;
EOF

# Create the launch script for proper window sizing
cat > /config/launch-ppg2-frontend.sh << 'EOF'
#!/bin/bash
# Launch Chromium with exact window size for streaming
chromium-browser \
  --app=http://frontend:9999 \
  --window-size=1280,720 \
  --window-position=0,0 \
  --disable-web-security \
  --disable-features=VizDisplayCompositor \
  --force-device-scale-factor=1 \
  --new-window \
  --no-first-run \
  --no-default-browser-check \
  --disable-default-apps \
  --disable-popup-blocking
EOF

chmod +x /config/launch-ppg2-frontend.sh

# PulseAudio Control shortcut
cat > /config/Desktop/Audio-Control.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Audio Control
Comment=PulseAudio Volume Control
Exec=pavucontrol
Icon=multimedia-volume-control
Terminal=false
Categories=AudioVideo;Audio;
EOF

# Virtual Audio Setup shortcut
cat > /config/Desktop/Setup-Virtual-Audio.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Setup Virtual Audio
Comment=Load virtual audio devices for streaming
Exec=gnome-terminal -- /config/load-virtual-audio.sh
Icon=audio-card
Terminal=true
Categories=AudioVideo;Audio;
EOF

# Make desktop files executable
chmod +x /config/Desktop/*.desktop

# Create a startup script for the user session
mkdir -p /config/.config/autostart

cat > /config/.config/autostart/pulseaudio-setup.desktop << 'EOF'
[Desktop Entry]
Type=Application
Name=PulseAudio Setup
Exec=/config/start-pulseaudio.sh
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
EOF

# Create PulseAudio startup script
cat > /config/start-pulseaudio.sh << 'EOF'
#!/bin/bash
# Start PulseAudio with proper configuration
echo "Starting PulseAudio setup..."

# Kill any existing PulseAudio processes
pulseaudio --kill 2>/dev/null || true
sleep 2

# Start PulseAudio
pulseaudio --start --log-target=syslog
sleep 3

# Wait for PulseAudio to be ready
while ! pactl info >/dev/null 2>&1; do
    echo "Waiting for PulseAudio to start..."
    sleep 1
done

echo "PulseAudio is running, loading virtual audio..."
/config/load-virtual-audio.sh

# Verify setup
echo "=== PulseAudio Setup Complete ==="
echo "Available sinks:"
pactl list sinks short
echo ""
echo "Available sources:"
pactl list sources short
echo ""
echo "Default sink: $(pactl get-default-sink 2>/dev/null || echo 'Not set')"
echo "Default source: $(pactl get-default-source 2>/dev/null || echo 'Not set')"
echo "================================="
EOF

chmod +x /config/start-pulseaudio.sh

# Set proper permissions
chown -R 1000:1000 /config/.config
chown -R 1000:1000 /config/Desktop

echo "PPG2 Webtop setup completed!"
echo ""
echo "Setup includes:"
echo "- OBS Studio with preconfigured scene for PPG2 frontend"
echo "- Chromium browser with 1280x720 launch script"
echo "- Virtual audio routing: Desktop Audio -> Virtual Speaker -> Virtual Microphone"
echo "- Desktop shortcuts for easy access"
echo ""
echo "Audio Setup:"
echo "- Virtual_Speaker: Default output for all desktop audio"
echo "- Virtual_Microphone_Source: Input for OBS to capture desktop audio"
echo ""
echo "To use:"
echo "1. Access webtop at http://localhost:3000"
echo "2. Login with username: streamer, password: streaming123"
echo "3. Wait for PulseAudio to initialize (check Audio Control)"
echo "4. Open OBS Studio from desktop"
echo "5. Open PPG2 Frontend from desktop shortcut (launches at 1280x720)"
echo "6. In OBS, add Audio Input Capture source using 'Virtual_Microphone_Source'"
echo "7. Configure your streaming settings in OBS"
