"""
Chat service for managing chat messages and broadcasting
"""
import logging
from typing import Dict, List, Optional, TYPE_CHECKING
import datetime

from ..core.state import app_state
from ..core.config import settings

if TYPE_CHECKING:
    from ..games.manager import GameManager

logger = logging.getLogger(__name__)


class ChatService:
    """Service for managing chat messages"""

    _game_manager: Optional['GameManager'] = None

    @classmethod
    def set_game_manager(cls, game_manager: 'GameManager'):
        """Set the game manager instance"""
        cls._game_manager = game_manager
    
    @staticmethod
    def add_message(username: str, message: str, timestamp: str = None) -> Dict:
        """Add a new chat message to the system"""
        if not timestamp:
            timestamp = datetime.datetime.now().isoformat()
        
        chat_data = {
            "username": username,
            "message": message,
            "timestamp": timestamp
        }
        
        # Store in shared memory
        app_state.chat_messages.append(chat_data)
        
        # Keep only the configured maximum number of messages
        if len(app_state.chat_messages) > settings.max_chat_messages:
            app_state.chat_messages = app_state.chat_messages[-settings.max_chat_messages:]
        
        logger.info(f"Chat message added from {username}: {message}")
        return chat_data
    
    @staticmethod
    async def broadcast_message(chat_data: Dict) -> int:
        """Broadcast a chat message to all connected WebSockets"""
        from .websocket import WebSocketService
        
        message = {
            "type": "chat_message",
            "data": chat_data
        }
        
        return await WebSocketService.broadcast(message)
    
    @staticmethod
    async def process_message(username: str, message: str, timestamp: str = None) -> Dict:
        """Process a new chat message and broadcast it"""
        # Add message to storage
        chat_data = ChatService.add_message(username, message, timestamp)

        # Broadcast to all connected clients - not anymore
        # broadcast_count = await ChatService.broadcast_message(chat_data)

        # Process game commands
        if ChatService._game_manager:
            await ChatService._game_manager.handle_chat_message(username, message)

        return {
            "status": "success",
            "message": "Chat message processed",
            "broadcast_count": 0,
            "data": chat_data
        }
    
    @staticmethod
    async def process_websocket_message(data: Dict):
        """Process a chat message received via WebSocket"""
        username = data.get("username", "Anonymous")
        message = data.get("message", "")
        timestamp = data.get("timestamp")
        
        if message.strip():
            await ChatService.process_message(username, message, timestamp)
    
    @staticmethod
    def get_recent_messages(count: int = None) -> List[Dict]:
        """Get recent chat messages"""
        if count is None:
            count = settings.recent_chat_messages
        
        return app_state.chat_messages[-count:]
    
    @staticmethod
    def get_message_count() -> int:
        """Get total number of stored chat messages"""
        return len(app_state.chat_messages)
    
    @staticmethod
    def clear_messages():
        """Clear all chat messages (admin function)"""
        app_state.chat_messages.clear()
        logger.info("All chat messages cleared")


# Convenience instance
chat_service = ChatService()
