"""
Database service for managing PostgreSQL connections
"""
import logging
from typing import Optional, Any
import psycopg2
from psycopg2.extras import RealDictCursor
from fastapi import HTTPException

from ..core.config import get_db_connection_params
from ..core.state import app_state

logger = logging.getLogger(__name__)


class DatabaseService:
    """Database service for managing connections and queries"""

    @staticmethod
    def get_connection() -> Any:
        """Get database connection with retry logic"""
        try:
            if app_state.db_connection is None or app_state.db_connection.closed:
                conn_params = get_db_connection_params()
                app_state.db_connection = psycopg2.connect(**conn_params)
                app_state.db_connection.autocommit = True
                logger.info("Database connection established")
            
            return app_state.db_connection
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise HTTPException(status_code=500, detail="Database connection failed")
    
    @staticmethod
    def execute_query(query: str, params: tuple = None, fetch: bool = False):
        """Execute a database query"""
        try:
            conn = DatabaseService.get_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                if fetch:
                    return cursor.fetchall()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise HTTPException(status_code=500, detail="Database query failed")
    
    @staticmethod
    def execute_query_one(query: str, params: tuple = None):
        """Execute a query and return one result"""
        try:
            conn = DatabaseService.get_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise HTTPException(status_code=500, detail="Database query failed")
    
    @staticmethod
    def health_check() -> bool:
        """Check if database connection is healthy"""
        try:
            conn = DatabaseService.get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    @staticmethod
    def close_connection():
        """Close the database connection"""
        if app_state.db_connection and not app_state.db_connection.closed:
            app_state.db_connection.close()
            app_state.db_connection = None
            logger.info("Database connection closed")


# Convenience instance
db = DatabaseService()
