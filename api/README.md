# Pump Plays Games API

A modular FastAPI application for collective gameplay - like Twitch Plays Pokemon but for multiple games!

## 🎮 Current Games

### Blackjack
- **Join Phase**: Players type "join" in chat during 30-second window
- **Voting Modes**: Democracy (most votes win) or Anarchy (random weighted selection) 
- **Player Elimination**: Only voters of winning action can vote in next round
- **Auto Progression**: Automatic card dealing, turn timers, game restart

## 🚀 Quick Start

1. **Start the server:**
```bash
python -m api.main
```

2. **Connect via WebSocket:**
```javascript
ws://localhost:8000/ws
```

3. **Join a game via chat:**
```json
{
  "type": "chat_message",
  "data": {
    "username": "<PERSON><PERSON><PERSON>", 
    "message": "join"
  }
}
```

4. **Vote for actions:**
```json
{
  "type": "chat_message",
  "data": {
    "username": "YourName",
    "message": "hit"  // or "stand", "double", "split"
  }
}
```

## 📡 WebSocket Events

All game events are broadcast in real-time:

### Game Lifecycle
- `join_phase_started` - Game open for players to join
- `player_joined` - Someone joined the game
- `game_started` - Actual gameplay begins  
- `game_ended` - Game finished with results

### Blackjack Specific
- `cards_dealt` - Initial cards distributed
- `vote_cast` - Player voted for an action
- `turn_ended` - Voting completed, action decided
- `action_executed` - Game action performed
- `eligible_players_updated` - Player elimination update

## 🛠️ Configuration

Environment variables:

```bash
# Game Timing
GAME_JOIN_PHASE_DURATION=30    # seconds for join phase
GAME_TURN_DURATION=20          # seconds per voting turn
GAME_AUTO_RESTART=true         # auto-start new games
GAME_RESTART_DELAY=5           # seconds between games

# Blackjack Settings  
BLACKJACK_DEMOCRACY_CHANCE=0.5 # probability of democracy vs anarchy

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/postgres
```

## 🏗️ Architecture

- **Base Game System**: Extensible framework for multiple game types
- **Game Manager**: Handles lifecycle, WebSocket integration, database storage
- **WebSocket Service**: Real-time event broadcasting
- **Database Layer**: Raw psycopg2 for game outcome storage

## 🔗 API Endpoints

- `GET /health` - System health check
- `GET /game/state` - Current game state
- `GET /game/status` - Game manager status
- `POST /game/start/{game_type}` - Start new game
- `POST /game/force-action/{action}` - Admin action execution
- `POST /game/end` - Force end current game

## 📊 Database Schema

Games are automatically stored in PostgreSQL:
- `blackjack_games` - Game instances
- `blackjack_events` - Action history  
- `user_winners` - Payout records
- `users` - Player accounts

## 🧪 Testing

```bash
# Test game logic
python api/test_game_flow.py

# Test API endpoints (server must be running)
python test_api.py

# Test WebSocket (server must be running)
python test_websocket.py
```