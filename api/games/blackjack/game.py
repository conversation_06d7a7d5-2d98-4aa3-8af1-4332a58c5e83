"""
Blackjack game logic and state management using the new base game architecture
"""
import json
import random
import logging
import time
from typing import List, Dict, Optional, Any
from ..base import BaseGame, GamePhase, VotingMode
from .models import Card, BlackjackGameState, BlackjackAction

logger = logging.getLogger(__name__)


class BlackjackGame(BaseGame):
    """Blackjack game logic and state management"""
    
    def __init__(self, game_config: Dict[str, Any] = None):
        self.deck: List[Card] = []
        super().__init__(game_config)
        self._initialize_deck()
    
    def create_initial_state(self) -> BlackjackGameState:
        """Create the initial blackjack game state"""
        return BlackjackGameState()
    
    def _initialize_deck(self):
        """Initialize a standard 52-card deck"""
        suits = ["hearts", "diamonds", "clubs", "spades"]
        ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        
        self.deck = [Card(rank=rank, suit=suit) for suit in suits for rank in ranks]
        random.shuffle(self.deck)
        logger.info("New deck initialized and shuffled")
    
    def _card_value(self, card: Card) -> int:
        """Get the numeric value of a card"""
        if card.rank in ["J", "Q", "K"]:
            return 10
        elif card.rank == "A":
            return 11  # Will be adjusted for aces later
        else:
            return int(card.rank)
    
    def _calculate_score(self, cards: List[Card]) -> int:
        """Calculate the score of a hand, handling aces properly"""
        score = 0
        aces = 0
        
        for card in cards:
            if card.rank == "A":
                aces += 1
                score += 11
            else:
                score += self._card_value(card)
        
        # Adjust for aces
        while score > 21 and aces > 0:
            score -= 10
            aces -= 1
        
        return score
    
    def _is_soft_hand(self, cards: List[Card]) -> bool:
        """Check if hand is soft (contains an ace counted as 11)"""
        score = 0
        aces = 0
        
        for card in cards:
            if card.rank == "A":
                aces += 1
                score += 11
            else:
                score += self._card_value(card)
        
        # If we have aces and score > 21, we would need to convert some
        # If after converting all aces to 1, we still have at least one ace
        # that could be 11 without busting, then it's soft
        while score > 21 and aces > 0:
            score -= 10
            aces -= 1
            
        # If we have aces remaining that are being counted as 11, it's soft
        return aces > 0 and score <= 21
    
    def _deal_card(self) -> Card:
        """Deal a card from the deck"""
        if not self.deck:
            self._initialize_deck()
        return self.deck.pop()
    
    def deal_initial_cards(self):
        """Deal initial cards to start the blackjack game"""
        if self.state.phase != GamePhase.PLAYING:
            return
        
        # Deal initial cards
        self.state.player_cards = [self._deal_card(), self._deal_card()]
        self.state.dealer_cards = [self._deal_card()]  # Dealer gets one visible card
        self.state.dealer_hidden_card = self._deal_card()  # Hidden hole card
        
        # Calculate scores
        self.state.player_score = self._calculate_score(self.state.player_cards)
        self.state.dealer_score = self._calculate_score(self.state.dealer_cards)  # Only visible card
        
        # Check for blackjack
        if self.state.player_score == 21:
            # Player blackjack - reveal dealer card and determine winner
            full_dealer_cards = self.state.dealer_cards + [self.state.dealer_hidden_card]
            full_dealer_score = self._calculate_score(full_dealer_cards)
            
            if full_dealer_score == 21:
                self.state.game_result = "push"
            else:
                self.state.game_result = "blackjack"
            
            self.state.dealer_cards = full_dealer_cards
            self.state.dealer_score = full_dealer_score
            self.state.dealer_hidden_card = None
            
            self.add_event("cards_dealt", {
                "player_cards": [card.dict() for card in self.state.player_cards],
                "dealer_cards": [card.dict() for card in self.state.dealer_cards],
                "player_score": self.state.player_score,
                "dealer_score": self.state.dealer_score,
                "result": self.state.game_result
            })
            return False  # Game over
        
        self.add_event("cards_dealt", {
            "player_cards": [card.dict() for card in self.state.player_cards],
            "dealer_cards": [card.dict() for card in self.state.dealer_cards],
            "player_score": self.state.player_score,
            "dealer_score": self.state.dealer_score
        })
        
        return True  # Game continues
    
    def get_valid_actions(self) -> List[str]:
        """Get valid actions for the current game state"""
        if self.state.phase != GamePhase.PLAYING:
            return []

        # For now, only hit and stand are available
        # TODO: Add double and split actions in future implementation
        actions = [BlackjackAction.HIT.value, BlackjackAction.STAND.value]

        return actions
    
    def execute_action(self, action: str) -> bool:
        """Execute a blackjack action and return True if game continues"""
        if self.state.phase != GamePhase.PLAYING:
            return False

        action_enum = BlackjackAction(action)

        if action_enum == BlackjackAction.HIT:
            return self._hit()
        elif action_enum == BlackjackAction.STAND:
            return self._stand()
        # TODO: Add double and split action handling in future implementation
        # elif action_enum == BlackjackAction.DOUBLE:
        #     return self._double()
        # elif action_enum == BlackjackAction.SPLIT:
        #     return self._split()

        return False
    
    def _hit(self) -> bool:
        """Player hits (takes another card)"""
        card = self._deal_card()
        self.state.player_cards.append(card)
        self.state.player_score = self._calculate_score(self.state.player_cards)
        
        self.add_event("action_executed", {
            "action": "hit",
            "new_card": card.dict(),
            "player_cards": [card.dict() for card in self.state.player_cards],
            "player_score": self.state.player_score
        })
        
        # Check for bust
        if self.state.player_score > 21:
            self.state.game_result = "player_busted"
            self.add_event("player_bust", {
                "player_score": self.state.player_score,
                "result": "player_busted",
                "reason": "Player busted with score over 21"
            })
            return False  # Game over
        
        # Check for hard 21 - automatically stand and proceed to dealer
        if self.state.player_score == 21:
            self.add_event("auto_stand", {
                "player_score": self.state.player_score,
                "reason": "Player reached 21, automatically standing"
            })
            return self._stand()  # Automatically stand and let dealer play
        
        return True  # Game continues
    
    def _stand(self) -> bool:
        """Player stands (dealer plays)"""
        # Reveal dealer's hidden card
        if self.state.dealer_hidden_card:
            self.state.dealer_cards.append(self.state.dealer_hidden_card)
            self.state.dealer_hidden_card = None
        
        # Dealer draws until 17 or higher (hits on soft 17)
        dealer_score = self._calculate_score(self.state.dealer_cards)
        while dealer_score < 17 or (dealer_score == 17 and self._is_soft_hand(self.state.dealer_cards)):
            card = self._deal_card()
            self.state.dealer_cards.append(card)
            dealer_score = self._calculate_score(self.state.dealer_cards)
            self.add_event("dealer_draws", {
                "card": card.dict(),
                "dealer_score": dealer_score
            })
        
        self.state.dealer_score = self._calculate_score(self.state.dealer_cards)
        
        # Determine winner
        if self.state.dealer_score > 21:
            self.state.game_result = "dealer_busted"
            reason = "Dealer busted with score over 21"
        elif self.state.dealer_score > self.state.player_score:
            self.state.game_result = "dealer_win"
            reason = f"Dealer wins with {self.state.dealer_score} vs {self.state.player_score}"
        elif self.state.player_score > self.state.dealer_score:
            self.state.game_result = "player_win"
            reason = f"Player wins with {self.state.player_score} vs {self.state.dealer_score}"
        else:
            self.state.game_result = "push"
            reason = f"Push - both have {self.state.player_score}"

        self.add_event("action_executed", {
            "action": "stand",
            "dealer_cards": [card.dict() for card in self.state.dealer_cards],
            "dealer_score": self.state.dealer_score,
            "result": self.state.game_result,
            "reason": reason
        })
        
        return False  # Game over
    
    # TODO: Implement double and split actions in future
    # def _double(self) -> bool:
    #     """Player doubles down"""
    #     if not self.state.can_double:
    #         return self._hit()  # Fallback to hit if can't double
    #
    #     card = self._deal_card()
    #     self.state.player_cards.append(card)
    #     self.state.player_score = self._calculate_score(self.state.player_cards)
    #
    #     self.add_event("action_executed", {
    #         "action": "double",
    #         "new_card": card.dict(),
    #         "player_cards": [card.dict() for card in self.state.player_cards],
    #         "player_score": self.state.player_score
    #     })
    #
    #     # After doubling, automatically stand if not bust
    #     if self.state.player_score > 21:
    #         self.state.game_result = "player_busted"
    #         self.add_event("player_bust", {
    #             "player_score": self.state.player_score,
    #             "result": "player_busted",
    #             "reason": "Player busted after doubling down"
    #         })
    #         return False  # Game over
    #     else:
    #         # Stand automatically after double
    #         return self._stand()
    #
    # def _split(self) -> bool:
    #     """Player splits (simplified - just treat as hit for now)"""
    #     # For simplicity, treat split as a hit
    #     # In a full implementation, this would create two hands
    #     self.add_event("action_executed", {
    #         "action": "split",
    #         "note": "Split treated as hit (simplified implementation)"
    #     })
    #     return self._hit()
    
    def get_game_result(self) -> Dict[str, Any]:
        """Get the final game result when game is finished"""
        # Determine reason based on result
        reason = ""
        if self.state.game_result == "player_busted":
            reason = f"Player busted with {self.state.player_score}"
        elif self.state.game_result == "dealer_busted":
            reason = f"Dealer busted with {self.state.dealer_score}"
        elif self.state.game_result == "blackjack":
            reason = "Player got blackjack!"
        elif self.state.game_result == "push":
            reason = f"Push - both have {self.state.player_score}"
        elif self.state.game_result == "player_win":
            reason = f"Player wins {self.state.player_score} vs {self.state.dealer_score}"
        elif self.state.game_result == "dealer_win":
            reason = f"Dealer wins {self.state.dealer_score} vs {self.state.player_score}"

        return {
            "result": self.state.game_result,
            "reason": reason,
            "player_score": self.state.player_score,
            "dealer_score": self.state.dealer_score,
            "player_cards": [card.dict() for card in self.state.player_cards],
            "dealer_cards": [card.dict() for card in self.state.dealer_cards],
            "winners": self.state.eligible_players if self.state.game_result in ["player_win", "blackjack", "dealer_busted"] else [],
            "total_players": len(self.state.joined_players)
        }
    
    async def store_game_result(self, db_connection) -> int:
        """Store the game result in database using raw psycopg2"""
        try:
            with db_connection.cursor() as cursor:
                # Insert blackjack_games record
                cursor.execute("""
                    INSERT INTO blackjack_games (created_at, ended_at, seed, token_pool)
                    VALUES (NOW(), NOW(), %s, %s)
                    RETURNING id
                """, (str(random.randint(1000000, 9999999)), 0))
                
                game_id = cursor.fetchone()[0]
                self.state.game_id = game_id
                
                # Store deal event
                cursor.execute("""
                    INSERT INTO blackjack_events (game_id, action, card, created_at)
                    VALUES (%s, %s, %s, NOW())
                """, (game_id, "deal", None))
                
                # Store all player cards dealt
                for card in self.state.player_cards:
                    cursor.execute("""
                        INSERT INTO blackjack_events (game_id, action, card, created_at)
                        VALUES (%s, %s, %s, NOW())
                    """, (game_id, "deal", json.dumps(card.to_json())))
                
                # Store all dealer cards dealt
                for card in self.state.dealer_cards:
                    cursor.execute("""
                        INSERT INTO blackjack_events (game_id, action, card, created_at)
                        VALUES (%s, %s, %s, NOW())
                    """, (game_id, "deal", json.dumps(card.to_json())))
                
                # Store game end event
                cursor.execute("""
                    INSERT INTO blackjack_events (game_id, action, card, created_at)
                    VALUES (%s, %s, %s, NOW())
                """, (game_id, "end", None))
                
                # Store winner records
                winners = self.state.eligible_players if self.state.game_result in ["player_win", "blackjack", "dealer_busted"] else []
                
                for username in self.state.joined_players:
                    # Get users               
                    cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
                    user_id = cursor.fetchone()[0]
                    
                    # Determine if this user won
                    is_winner = username in winners
                    payout = 1.0 if is_winner else 0.0
                    
                    cursor.execute("""
                        INSERT INTO user_winners (game_id, user_id, payout, result)
                        VALUES (%s, %s, %s, %s)
                    """, (game_id, user_id, payout, self.state.game_result))
                
                logger.info(f"Blackjack game {game_id} stored in database")
                return game_id
                
        except Exception as e:
            logger.error(f"Failed to store game result: {e}")
            raise
    
    async def end_join_phase(self):
        """Override base class method to handle blackjack-specific initialization"""
        if self.state.phase != GamePhase.JOINING:
            return
        
        self.add_event("join_phase_ended", {
            "joined_players": self.state.joined_players,
            "player_count": len(self.state.joined_players)
        })
        
        if len(self.state.joined_players) == 0:
            self.add_event("game_cancelled", {"reason": "No players joined, starting new join phase"})
            # Start a new join phase instead of going to WAITING
            await self.start_join_phase()
            return
        
        # Set voting mode for this game
        self.state.voting_mode = (VotingMode.DEMOCRACY 
                                 if random.random() < self.state.voting_mode_chance 
                                 else VotingMode.ANARCHY)
        
        self.state.phase = GamePhase.PLAYING
        self.state.phase_start_time = time.time()
        
        self.add_event("game_started", {
            "voting_mode": self.state.voting_mode.value,
            "player_count": len(self.state.joined_players)
        })
        
        # Deal initial cards and start game
        await self.start_game_after_join()
    
    async def start_game_after_join(self):
        """Called after join phase ends to start the actual game"""
        if not self.deal_initial_cards():
            # Game ended immediately (blackjack)
            await self.end_game()
        else:
            # Start first turn
            await self.start_turn()
