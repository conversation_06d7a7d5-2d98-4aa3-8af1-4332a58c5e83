"""
Pydantic models specific to blackjack game
"""
from typing import List, Dict, Optional
from pydantic import BaseModel, Field
from enum import Enum
from ..base import BaseGameState


class BlackjackAction(str, Enum):
    """Valid blackjack actions"""
    HIT = "hit"
    STAND = "stand"
    # TODO: Add DOUBLE and SPLIT actions in future implementation
    # DOUBLE = "double"
    # SPLIT = "split"


class Card(BaseModel):
    """Model for a playing card"""
    rank: str = Field(..., description="Card rank (A, 2-10, J, Q, K)")
    suit: str = Field(..., description="Card suit (hearts, diamonds, clubs, spades)")
    
    def to_json(self) -> Dict[str, str]:
        """Convert card to JSON format for database storage"""
        return {"rank": self.rank, "suit": self.suit}


class BlackjackVote(BaseModel):
    """Model for blackjack voting"""
    username: str = Field(..., min_length=1, max_length=50)
    action: BlackjackAction
    mode: str = Field(..., description="Voting mode (anarchy, democracy)")


class BlackjackGameState(BaseGameState):
    """Model for blackjack game state extending base game state"""
    player_cards: List[Card] = []
    dealer_cards: List[Card] = []
    player_score: int = 0
    dealer_score: int = 0
    game_result: Optional[str] = None  # player_win, dealer_win, push, blackjack
    dealer_hidden_card: Optional[Card] = None  # Hidden hole card
    # TODO: Add fields for future double/split implementation
    # can_double: bool = False
    # can_split: bool = False
    # player_hands: List[List[Card]] = []  # For split hands (future enhancement)
    # current_hand: int = 0  # Which hand is being played in split scenario
