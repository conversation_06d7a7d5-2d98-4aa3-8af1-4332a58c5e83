"""
Base game interface and lifecycle management for all games
"""
import abc
import random
import asyncio
import logging
import time
from enum import Enum
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class GamePhase(str, Enum):
    """Game lifecycle phases"""
    WAITING = "waiting"           # Waiting for players to join
    JOINING = "joining"           # Join phase active
    PLAYING = "playing"           # Game in progress
    FINISHED = "finished"         # Game completed


class VotingMode(str, Enum):
    """Voting modes"""
    DEMOCRACY = "democracy"       # Most votes wins
    ANARCHY = "anarchy"           # Random vote selected


class GameEvent(BaseModel):
    """Base game event model"""
    event_type: str
    timestamp: float
    data: Dict[str, Any]


class PlayerAction(BaseModel):
    """Player action with metadata"""
    username: str
    action: str
    timestamp: float
    mode: VotingMode


class BaseGameState(BaseModel, abc.ABC):
    """Base game state that all games must implement"""
    game_id: Optional[int] = None
    phase: GamePhase = GamePhase.WAITING
    joined_players: List[str] = []
    eligible_players: List[str] = []  # Players eligible to vote (survived previous round)
    current_votes: Dict[str, List[str]] = {}  # action -> list of usernames
    voting_mode: VotingMode = VotingMode.DEMOCRACY
    voting_mode_chance: float = 0.5  # Probability of democracy vs anarchy
    
    # Timing
    phase_start_time: float = 0
    join_phase_duration: int = 30  # seconds
    turn_duration: int = 20        # seconds
    
    # Game-specific state should be added in subclasses


class BaseGame(abc.ABC):
    """Base game class that all games must implement"""
    
    def __init__(self, game_config: Dict[str, Any] = None):
        self.config = game_config or {}
        self.state: BaseGameState = self.create_initial_state()
        
        # Apply config to state
        if "join_phase_duration" in self.config:
            self.state.join_phase_duration = self.config["join_phase_duration"]
        if "turn_duration" in self.config:
            self.state.turn_duration = self.config["turn_duration"]
        if "voting_mode_chance" in self.config:
            self.state.voting_mode_chance = self.config["voting_mode_chance"]
        
        self.event_queue: List[GameEvent] = []
        self.turn_task: Optional[asyncio.Task] = None
        self.join_task: Optional[asyncio.Task] = None
        
    @abc.abstractmethod
    def create_initial_state(self) -> BaseGameState:
        """Create the initial game state"""
        pass
    
    @abc.abstractmethod
    def get_valid_actions(self) -> List[str]:
        """Get valid actions for the current game state"""
        pass
    
    @abc.abstractmethod
    def execute_action(self, action: str) -> bool:
        """Execute a game action and return True if game continues"""
        pass
    
    @abc.abstractmethod
    def get_game_result(self) -> Dict[str, Any]:
        """Get the final game result when game is finished"""
        pass
    
    @abc.abstractmethod
    async def store_game_result(self, db_connection) -> int:
        """Store the game result in database and return game_id"""
        pass
    
    def add_event(self, event_type: str, data: Dict[str, Any]):
        """Add an event to the game event queue"""
        event = GameEvent(
            event_type=event_type,
            timestamp=time.time(),
            data=data
        )
        self.event_queue.append(event)
        logger.info(f"Game event: {event_type} - {data}")
        
        # Broadcast event if game manager is available
        if hasattr(self, '_game_manager') and self._game_manager:
            # Schedule the broadcast to run in the event loop
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self._game_manager._broadcast_game_event(event_type, data))
            except RuntimeError:
                # No event loop running, skip broadcasting
                logger.warning(f"No event loop running, skipping broadcast for {event_type}")
                pass
    
    def join_game(self, username: str) -> bool:
        """Add a player to the game during join phase"""
        if self.state.phase != GamePhase.JOINING:
            return False
            
        if username not in self.state.joined_players:
            self.state.joined_players.append(username)
            self.state.eligible_players.append(username)
            self.add_event("player_joined", {"username": username})
            logger.info(f"Player {username} joined the game")
            
            # Start the join timer if this is the first player
            if len(self.state.joined_players) == 1:
                self.add_event("join_timer_started", {
                    "duration": self.state.join_phase_duration,
                    "message": f"First player joined! Join phase will end in {self.state.join_phase_duration}s"
                })
                
                # Start join timer
                if self.join_task:
                    self.join_task.cancel()
                
                self.join_task = asyncio.create_task(self._join_phase_timer())
            
            return True
        return False
    
    def add_vote(self, username: str, action: str) -> bool:
        """Add a player's vote for an action"""
        if self.state.phase != GamePhase.PLAYING:
            return False

        # Check if player is eligible to vote
        if username not in self.state.eligible_players:
            logger.warning(f"Player {username} not eligible to vote")
            return False

        # Check if action is valid
        if action not in self.get_valid_actions():
            logger.warning(f"Invalid action {action} from {username}")
            return False

        # Check if user has already voted for this turn (vote is final)
        for act, voters in self.state.current_votes.items():
            if username in voters:
                logger.warning(f"Player {username} has already voted for {act} - vote is final")
                return False

        # Add new vote
        if action not in self.state.current_votes:
            self.state.current_votes[action] = []
        self.state.current_votes[action].append(username)

        self.add_event("vote_cast", {
            "username": username,
            "action": action,
            "mode": self.state.voting_mode.value
        })

        logger.info(f"Vote added: {username} -> {action}")
        return True
    
    def determine_winning_action(self) -> Optional[str]:
        """Determine the winning action based on voting mode"""
        if not self.state.current_votes:
            return None
        
        if self.state.voting_mode == VotingMode.DEMOCRACY:
            # Most votes wins
            max_votes = max(len(voters) for voters in self.state.current_votes.values())
            candidates = [action for action, voters in self.state.current_votes.items() 
                         if len(voters) == max_votes]
            
            if len(candidates) == 1:
                return candidates[0]
            else:
                # Tie-breaker: random selection
                return random.choice(candidates)
        
        else:  # ANARCHY
            # Random selection from all votes
            all_votes = []
            for action, voters in self.state.current_votes.items():
                all_votes.extend([action] * len(voters))
            
            if all_votes:
                return random.choice(all_votes)
        
        return None
    
    def update_eligible_players(self, winning_action: str):
        """Update eligible players to only those who voted for winning action"""
        if winning_action and winning_action in self.state.current_votes:
            self.state.eligible_players = self.state.current_votes[winning_action].copy()
        else:
            self.state.eligible_players = []
        
        self.add_event("eligible_players_updated", {
            "winning_action": winning_action,
            "eligible_players": self.state.eligible_players,
            "survivor_count": len(self.state.eligible_players)
        })
    
    def clear_votes(self):
        """Clear all current votes"""
        self.state.current_votes = {}
    
    async def start_join_phase(self) -> None:
        """Start the join phase - timer starts when first player joins"""
        self.state.phase = GamePhase.JOINING
        self.state.phase_start_time = time.time()
        
        self.add_event("join_phase_started", {
            "duration": self.state.join_phase_duration,
            "message": f"Game starting! Type 'join' to participate! Timer starts when first player joins."
        })
        
        # Don't start timer yet - will start when first player joins
    
    async def _join_phase_timer(self):
        """Timer for join phase"""
        try:
            await asyncio.sleep(self.state.join_phase_duration)
            await self.end_join_phase()
        except asyncio.CancelledError:
            pass
    
    async def end_join_phase(self):
        """End join phase and start game"""
        if self.state.phase != GamePhase.JOINING:
            return
        
        self.add_event("join_phase_ended", {
            "joined_players": self.state.joined_players,
            "player_count": len(self.state.joined_players)
        })
        
        if len(self.state.joined_players) == 0:
            self.add_event("game_cancelled", {"reason": "No players joined, starting new join phase"})
            # Start a new join phase instead of going to WAITING
            await self.start_join_phase()
            return
        
        # Set voting mode for this game
        self.state.voting_mode = (VotingMode.DEMOCRACY 
                                 if random.random() < self.state.voting_mode_chance 
                                 else VotingMode.ANARCHY)
        
        self.state.phase = GamePhase.PLAYING
        self.state.phase_start_time = time.time()
        
        self.add_event("game_started", {
            "voting_mode": self.state.voting_mode.value,
            "player_count": len(self.state.joined_players)
        })
        
        # Start first turn
        await self.start_turn()
    
    async def start_turn(self):
        """Start a new turn with timer"""
        if self.state.phase != GamePhase.PLAYING:
            return
        
        self.state.phase_start_time = time.time()
        valid_actions = self.get_valid_actions()
        
        self.add_event("turn_started", {
            "eligible_players": self.state.eligible_players,
            "valid_actions": valid_actions,
            "duration": self.state.turn_duration,
            "voting_mode": self.state.voting_mode.value
        })
        
        # Start turn timer
        if self.turn_task:
            self.turn_task.cancel()
        
        self.turn_task = asyncio.create_task(self._turn_timer())
    
    async def _turn_timer(self):
        """Timer for turn duration"""
        try:
            await asyncio.sleep(self.state.turn_duration)
            await self.end_turn()
        except asyncio.CancelledError:
            pass
    
    async def end_turn(self):
        """End the current turn and execute winning action"""
        if self.state.phase != GamePhase.PLAYING:
            return
        
        winning_action = self.determine_winning_action()
        
        self.add_event("turn_ended", {
            "votes": dict(self.state.current_votes),
            "winning_action": winning_action,
            "voting_mode": self.state.voting_mode.value
        })
        
        if winning_action:
            # Update eligible players before executing action
            self.update_eligible_players(winning_action)
            
            # Execute the action
            game_continues = self.execute_action(winning_action)
            
            if not game_continues:
                await self.end_game()
                return
        
        # Clear votes and start next turn
        self.clear_votes()
        
        # Check if anyone is still eligible to vote
        if not self.state.eligible_players:
            self.add_event("no_eligible_players", {"message": "No players remaining to vote"})
            await self.end_game()
            return
        
        # Start next turn
        await self.start_turn()
    
    async def end_game(self):
        """End the game and store results"""
        self.state.phase = GamePhase.FINISHED

        # Cancel any running timers
        if self.turn_task:
            self.turn_task.cancel()
        if self.join_task:
            self.join_task.cancel()

        result = self.get_game_result()

        self.add_event("game_ended", {
            "result": result,
            "final_players": self.state.eligible_players
        })

        logger.info(f"Game ended with result: {result}")

        # Notify game manager that game has ended for auto-restart
        if hasattr(self, '_game_manager') and self._game_manager:
            import asyncio
            try:
                # Schedule the game manager's auto-restart logic to run
                loop = asyncio.get_running_loop()
                loop.create_task(self._game_manager._handle_game_end())
            except RuntimeError:
                logger.warning("No event loop running, cannot notify game manager of game end")
    
    def get_state_dict(self) -> Dict[str, Any]:
        """Get the current game state as a dictionary"""
        state_dict = self.state.dict()
        state_dict["events"] = [event.dict() for event in self.event_queue[-10:]]  # Last 10 events
        return state_dict