"""
Setup and initialization for the application services
"""
import logging
from ..games.manager import game_manager
from ..services.websocket import ws_service

logger = logging.getLogger(__name__)


def initialize_services():
    """Initialize and wire up all application services"""
    # Set up the dependency between websocket service and game manager
    ws_service.set_game_manager(game_manager)

    # Set up the dependency between chat service and game manager
    from ..services.chat import ChatService
    ChatService.set_game_manager(game_manager)

    logger.info("Application services initialized successfully")


def setup_game_types():
    """Register all available game types with the game manager"""
    try:
        # Import and register blackjack game
        from ..games.blackjack.game import BlackjackGame
        
        blackjack_config = {
            "min_players": 1,
            "max_players": 50,
            "join_timeout": 30,
            "turn_timeout": 15,
            "voting_mode": "democracy"  # or "anarchy"
        }
        
        game_manager.register_game_type("blackjack", BlackjackGame, blackjack_config)
        
        logger.info("Game types registered successfully")
        
    except ImportError as e:
        logger.error(f"Failed to import game types: {e}")
    except Exception as e:
        logger.error(f"Failed to register game types: {e}")


def initialize_application():
    """Complete application initialization"""
    initialize_services()
    setup_game_types()
    logger.info("Application initialization complete")
