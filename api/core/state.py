"""
Global application state management
"""
from typing import Dict, List, Optional, Any
from fastapi import WebSocket
import psycopg2


class AppState:
    """Global application state shared between requests"""
    
    def __init__(self):
        # Database connection
        self.db_connection: Optional[psycopg2.connection] = None
        
        # WebSocket connections
        self.connected_websockets: List[WebSocket] = []
        
        # Chat system
        self.chat_messages: List[Dict] = []
        


# Global state instance
app_state = AppState()
