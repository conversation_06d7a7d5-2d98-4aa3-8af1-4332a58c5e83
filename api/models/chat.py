"""
Pydantic models for chat functionality
"""
from typing import Optional
from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """Model for incoming chat messages"""
    username: str = Field(..., min_length=1, max_length=50, description="Username of the message sender")
    message: str = Field(..., min_length=1, max_length=500, description="Chat message content")
    timestamp: Optional[str] = Field(None, description="ISO timestamp of the message")


class ChatResponse(BaseModel):
    """Model for chat API responses"""
    status: str
    message: str
    broadcast_count: int
    data: dict


class RecentChatResponse(BaseModel):
    """Model for recent chat messages response"""
    messages: list
    total_count: int
