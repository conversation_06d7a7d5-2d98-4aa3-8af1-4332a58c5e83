"""
Pydantic models for game functionality
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class GameAction(BaseModel):
    """Model for game actions"""
    action: str = Field(..., description="The action to perform")
    username: str = Field(..., min_length=1, max_length=50, description="<PERSON>rna<PERSON> performing the action")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional action data")


class GameStateResponse(BaseModel):
    """Model for game state responses"""
    game_type: Optional[str]
    game_state: Optional[Dict[str, Any]]
    phase: str
    connected_clients: int


class HealthResponse(BaseModel):
    """Model for health check responses"""
    status: str
    database: str
    active_connections: int
    chat_messages_count: int
    error: Optional[str] = None
