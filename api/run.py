#!/usr/bin/env python3
"""
Startup script for the Pump Plays Games API
"""
import sys
import os
import logging

# Add the parent directory to the path so we can import api modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import uvicorn
from api.core.config import settings

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
    logger.info(f"Starting Pump Plays Games API on {settings.api_host}:{settings.api_port}")
    logger.info(f"Workers: {settings.api_workers}")
    logger.info(f"Log level: {settings.log_level}")

    uvicorn.run(
        "api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=settings.api_workers,
        log_level=settings.log_level.lower(),
        reload=False  # Disable reload for production
    )
