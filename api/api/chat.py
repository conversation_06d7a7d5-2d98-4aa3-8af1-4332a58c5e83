"""
Chat API routes
"""
from fastapi import APIRouter, HTTPException
from ..models.chat import <PERSON><PERSON><PERSON>ess<PERSON>, ChatResponse, RecentChatResponse
from ..services.chat import chat_service

router = APIRouter(prefix="/chat", tags=["chat"])


@router.post("/", response_model=ChatResponse)
async def send_chat_message(message: ChatMessage):
    """Send a new chat message"""
    try:
        result = await chat_service.process_message(
            username=message.username,
            message=message.message,
            timestamp=message.timestamp
        )
        return ChatResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process chat message: {str(e)}")


@router.get("/recent", response_model=RecentChatResponse)
async def get_recent_chat():
    """Get recent chat messages"""
    try:
        messages = chat_service.get_recent_messages()
        total_count = chat_service.get_message_count()
        
        return RecentChatResponse(
            messages=messages,
            total_count=total_count
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat messages: {str(e)}")


@router.delete("/clear")
async def clear_chat():
    """Clear all chat messages (admin endpoint)"""
    try:
        chat_service.clear_messages()
        return {"status": "success", "message": "All chat messages cleared"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear chat messages: {str(e)}")
