"""
Main API routes
"""
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from ..models.game import GameStateResponse, HealthResponse
from ..services.database import db
from ..services.websocket import ws_service
from ..core.state import app_state
import logging

router = APIRouter()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        db_healthy = db.health_check()
        
        return HealthResponse(
            status="healthy" if db_healthy else "unhealthy",
            database="connected" if db_healthy else "disconnected",
            active_connections=ws_service.get_connection_count(),
            chat_messages_count=len(app_state.chat_messages)
        )
    except Exception as e:
        return HealthResponse(
            status="unhealthy",
            database="disconnected",
            active_connections=0,
            chat_messages_count=0,
            error=str(e)
        )


@router.get("/game/state", response_model=GameStateResponse)
async def get_game_state():
    """Get current game state"""
    from ..games.manager import game_manager
    
    current_state = game_manager.get_current_state()
    
    return GameStateResponse(
        game_type=current_state.get("game_type"),
        game_state=current_state.get("game_state"),
        phase=current_state.get("phase", "waiting"),
        connected_clients=ws_service.get_connection_count()
    )


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await ws_service.connect(websocket)

    try:
        # Send initial state
        await ws_service.send_initial_state(websocket)
        
        # Handle incoming messages
        while True:
            data = await websocket.receive_json()
            await ws_service.handle_message(websocket, data)
            
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logging.getLogger(__name__).error(f"WebSocket error: {e}")
    finally:
        ws_service.disconnect(websocket)
