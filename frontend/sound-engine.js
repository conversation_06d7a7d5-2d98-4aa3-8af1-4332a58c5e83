/**
 * Simple Sound Engine for Pump Plays Blackjack
 * Designed for easy integration and future gacha-style sound effects
 */

class SoundEngine {
    constructor() {
        this.sounds = new Map();
        this.volume = 0.7;
        this.enabled = true;
        this.audioContext = null;
        this.masterGain = null;
        
        // Initialize Web Audio API
        this.initAudioContext();
        
        // Sound categories for organization
        this.categories = {
            CARD: 'card',
            UI: 'ui', 
            GAME: 'game',
            GACHA: 'gacha',
            AMBIENT: 'ambient'
        };
        
        console.log('Sound Engine initialized');
    }
    
    initAudioContext() {
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create master gain node for volume control
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            this.masterGain.gain.value = this.volume;
            
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.audioContext = null;
        }
    }
    
    /**
     * Load a sound file
     * @param {string} name - Unique identifier for the sound
     * @param {string} url - Path to the sound file
     * @param {string} category - Sound category (optional)
     * @param {Object} options - Additional options (loop, volume, etc.)
     */
    async loadSound(name, url, category = this.categories.GAME, options = {}) {
        try {
            const audio = new Audio(url);
            audio.preload = 'auto';
            
            // Set default options
            const soundOptions = {
                loop: options.loop || false,
                volume: options.volume || 1.0,
                category: category,
                audio: audio,
                loaded: false
            };
            
            // Wait for audio to load
            await new Promise((resolve, reject) => {
                audio.addEventListener('canplaythrough', () => {
                    soundOptions.loaded = true;
                    resolve();
                });
                audio.addEventListener('error', reject);
                audio.load();
            });
            
            this.sounds.set(name, soundOptions);
            console.log(`Sound loaded: ${name} (${category})`);
            
        } catch (error) {
            console.warn(`Failed to load sound ${name}:`, error);
        }
    }
    
    /**
     * Play a sound
     * @param {string} name - Sound identifier
     * @param {Object} options - Playback options (volume, loop override, etc.)
     */
    play(name, options = {}) {
        if (!this.enabled) return;
        
        const sound = this.sounds.get(name);
        if (!sound || !sound.loaded) {
            console.warn(`Sound not found or not loaded: ${name}`);
            return;
        }
        
        try {
            const audio = sound.audio.cloneNode();
            audio.volume = (options.volume || sound.volume) * this.volume;
            audio.loop = options.loop !== undefined ? options.loop : sound.loop;
            
            // Play the sound
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.warn(`Failed to play sound ${name}:`, error);
                });
            }
            
            return audio;
            
        } catch (error) {
            console.warn(`Error playing sound ${name}:`, error);
        }
    }
    
    /**
     * Stop a specific sound instance
     * @param {HTMLAudioElement} audioInstance - The audio instance to stop
     */
    stop(audioInstance) {
        if (audioInstance) {
            audioInstance.pause();
            audioInstance.currentTime = 0;
        }
    }
    
    /**
     * Stop all sounds in a category
     * @param {string} category - Category to stop
     */
    stopCategory(category) {
        // This is a simplified implementation
        // In a more complex system, we'd track active instances
        console.log(`Stopping all sounds in category: ${category}`);
    }
    
    /**
     * Set master volume
     * @param {number} volume - Volume level (0.0 to 1.0)
     */
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.volume;
        }
    }
    
    /**
     * Enable/disable sound engine
     * @param {boolean} enabled - Whether sounds should play
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log(`Sound engine ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Preload common game sounds
     * This method will be called to load sounds when they become available
     */
    async preloadGameSounds() {
        // Placeholder for future sound loading
        // When sound files are added, they can be loaded here
        
        const soundsToLoad = [
            // Card sounds
            { name: 'card_deal', url: 'sounds/card_deal.mp3', category: this.categories.CARD },
            { name: 'card_flip', url: 'sounds/card_flip.mp3', category: this.categories.CARD },
            { name: 'card_shuffle', url: 'sounds/card_shuffle.mp3', category: this.categories.CARD },
            
            // UI sounds
            { name: 'button_click', url: 'sounds/button_click.mp3', category: this.categories.UI },
            { name: 'vote_cast', url: 'sounds/vote_cast.mp3', category: this.categories.UI },
            
            // Game sounds
            { name: 'game_start', url: 'sounds/game_start.mp3', category: this.categories.GAME },
            { name: 'game_win', url: 'sounds/game_win.mp3', category: this.categories.GAME },
            { name: 'game_lose', url: 'sounds/game_lose.mp3', category: this.categories.GAME },
            { name: 'blackjack', url: 'sounds/blackjack.mp3', category: this.categories.GAME },
            { name: 'bust', url: 'sounds/bust.mp3', category: this.categories.GAME },
            
            // Gacha sounds (for future use)
            { name: 'gacha_roll', url: 'sounds/gacha_roll.mp3', category: this.categories.GACHA },
            { name: 'rare_drop', url: 'sounds/rare_drop.mp3', category: this.categories.GACHA },
            { name: 'legendary_drop', url: 'sounds/legendary_drop.mp3', category: this.categories.GACHA },
            
            // Ambient sounds
            { name: 'casino_ambient', url: 'sounds/casino_ambient.mp3', category: this.categories.AMBIENT, options: { loop: true, volume: 0.3 } }
        ];
        
        // Only attempt to load sounds that actually exist
        for (const soundDef of soundsToLoad) {
            try {
                await this.loadSound(soundDef.name, soundDef.url, soundDef.category, soundDef.options);
            } catch (error) {
                // Silently fail for missing sound files
                console.log(`Sound file not found (this is expected): ${soundDef.url}`);
            }
        }
    }
    
    /**
     * Play a sequence of sounds with timing
     * @param {Array} sequence - Array of {sound, delay} objects
     */
    async playSequence(sequence) {
        for (const step of sequence) {
            if (step.delay) {
                await new Promise(resolve => setTimeout(resolve, step.delay));
            }
            if (step.sound) {
                this.play(step.sound, step.options);
            }
        }
    }
    
    /**
     * Create a sound effect for card dealing
     */
    playCardDeal() {
        this.play('card_deal');
    }
    
    /**
     * Create a sound effect for card shuffling
     */
    playCardShuffle() {
        this.play('card_shuffle');
    }
    
    /**
     * Create a sound effect for voting
     */
    playVoteCast() {
        this.play('vote_cast');
    }
    
    /**
     * Create a sound effect for game events
     */
    playGameEvent(eventType) {
        switch (eventType) {
            case 'start':
                this.play('game_start');
                break;
            case 'win':
                this.play('game_win');
                break;
            case 'lose':
                this.play('game_lose');
                break;
            case 'blackjack':
                this.play('blackjack');
                break;
            case 'bust':
                this.play('bust');
                break;
            default:
                console.log(`Unknown game event sound: ${eventType}`);
        }
    }
}

// Create global sound engine instance
window.soundEngine = new SoundEngine();

// Initialize sounds when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Preload sounds (will silently fail for missing files)
    window.soundEngine.preloadGameSounds();
});
