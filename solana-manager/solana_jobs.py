import os
import logging
from typing import List, <PERSON><PERSON>, Optional
from decimal import Decimal
from datetime import datetime, timedelta
import httpx
import asyncio
from solders.pubkey import Pubkey
from solders.keypair import Keypair
from solders.transaction import VersionedTransaction
from solders.message import MessageV0
from solders.compute_budget import set_compute_unit_limit, set_compute_unit_price
from utils import send_and_confirm_transaction_with_priority_fee
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed
from solana.rpc.types import TxOpts
from spl.token.async_client import AsyncToken
from spl.token.constants import TOKEN_PROGRAM_ID
from spl.token.instructions import burn, BurnParams, transfer, TransferParams

logger = logging.getLogger(__name__)

# Initialize Solana clients
PRIVATE_KEY = os.getenv('PK', '3jTT9AagVQN6TLbNS7H4Hx6s1xkd3jp1hGBkudVz6Evxq9GvMZmoaTVZ5XrqzfUN3ENKdxwQrefwjX6CTtL8ng7B')
TOKEN_ADDRESS = os.getenv('TOKEN_ADDRESS', '9jKKX2KAY2jPM7ck7G7cjRBKwi8Kh3LRn1UvBTiMpump')

keypair = Keypair.from_base58_string(PRIVATE_KEY)
token_mint = Pubkey.from_string(TOKEN_ADDRESS)
client = AsyncClient("https://mainnet.helius-rpc.com/?api-key=c83da65b-b753-44da-86b5-b0c1534539fa")
token_client = AsyncToken(client, token_mint, TOKEN_PROGRAM_ID, keypair)

async def store_pubkey(db):
    """Store our pubkey in the database using an update statement, the singular row already exists"""
    async with db.cursor() as acur:
        await acur.execute("""
            UPDATE solana_config SET public_key = %s
        """, (str(keypair.pubkey()),))
        await db.commit()
    
    logger.info(f"Stored pubkey: {keypair.pubkey()}")

async def process_payouts(db, job_id: str):
    """Process pending payouts"""
    # Get pending payouts
    async with db.cursor() as acur:
        row = await acur.execute("""select token_account_address from token_accounts where wallet_address = %s limit 1""", (str(keypair.pubkey()),))
        our_ata = await row.fetchone()

        payouts = await acur.execute("""
            SELECT uw.id, uw.payout, ta.token_account_address
            FROM user_winners uw
            JOIN users u ON uw.user_id = u.id
            JOIN token_accounts ta ON u.wallet_address = ta.wallet_address
            WHERE uw.tx_hash IS NULL AND ta.token_account_address IS NOT NULL AND ta.token_account_address != %s
            ORDER BY uw.created_at ASC
        """, (our_ata[0],))
        rows = await payouts.fetchall()


    
    if not rows:
        return

    # Create batch transfer
    transfers = [(row[2], int(row[1])) for row in rows]
    winner_ids = [row[0] for row in rows]

    # For every batches of 20 in transfers
    logger.info(f"Processing  {len(transfers)} payouts in {len(transfers) // 20 + 1} batches")
    for i in range(0, len(transfers), 20):
        batch = transfers[i:i+20]
        batch_winner_ids = winner_ids[i:i+20]
        await send_batch_transfer(batch, our_ata[0], db, batch_winner_ids)
    
async def send_batch_transfer(transfers: List[Tuple[str, int]],
                              our_ata: str,
                              db,
                              winner_ids: List[int]):
# Build transfer instructions (without priority fee - will be added automatically)
    instructions = []
    
    for token_account, amount in transfers:
        transfer_ix = transfer(TransferParams(
            source=Pubkey.from_string(our_ata),
            dest=Pubkey.from_string(token_account),
            owner=keypair.pubkey(),
            amount=amount,
            program_id=TOKEN_PROGRAM_ID
        ))
        instructions.append(transfer_ix)
    
    # Send transaction with dynamic priority fee
    try:
        signature = await send_and_confirm_transaction_with_priority_fee(
            client=client,
            payer=keypair,
            instructions=instructions,
            priority_level="High",  # Use High priority for payouts
            compute_unit_limit=200000
        )
        
        # Update database
        async with db.cursor() as acur:
            await acur.execute(
                "UPDATE user_winners SET tx_hash = %s WHERE id = ANY(%s)",
                (signature, winner_ids)
            )
        await db.commit()
        logger.info(f"Processed {len(transfers)} payouts: {signature}")
        
    except Exception as e:
        logger.error(f"Failed to process payouts: {e}")
        raise

async def process_burns(db, job_id: str):
    """Process token burns"""
    async with db.cursor() as acur:
        burns = await acur.execute("""
            SELECT id, amount FROM token_burns 
            WHERE tx_hash IS NULL 
            ORDER BY created_at ASC LIMIT 1
        """)
        row = await burns.fetchone()
        if not row:
            return
        burn_id, amount = row

        row = await acur.execute("""
            select token_account_address from token_accounts where wallet_address = %s limit 1""", (str(keypair.pubkey()),))
        token_account = await row.fetchone()
        token_account = token_account[0]

    if not amount:
        print("No amount")
        return
    
    if not token_account:
        print("No token account")
        return

    
    # Create burn instruction
    burn_ix = burn(BurnParams(
        program_id=TOKEN_PROGRAM_ID,
        account=Pubkey.from_string(token_account),
        mint=Pubkey.from_string(TOKEN_ADDRESS),
        owner=keypair.pubkey(),
        amount=int(amount)
    ))
    
    # Send burn transaction with dynamic priority fee
    try:
        signature = await send_and_confirm_transaction_with_priority_fee(
            client=client,
            payer=keypair,
            instructions=[burn_ix],
            priority_level="Medium",  # Use Medium priority for burns
            compute_unit_limit=50000
        )
        async with db.cursor() as acur:
            await acur.execute(
                "UPDATE token_burns SET tx_hash = %s WHERE id = %s",
                (signature, burn_id)
            )
        await db.commit()
        logger.info(f"Burned {amount} tokens: {signature}")
        
    except Exception as e:
        logger.error(f"Failed to burn tokens: {e}")
        raise

async def fetch_user_accounts(db, job_id: str):
    """Fetch all token holders from Helius"""
    
    helius_url = "https://mainnet.helius-rpc.com/?api-key=c83da65b-b753-44da-86b5-b0c1534539fa"
    page = 1
    
    # Clear existing data (no historical records needed)
    await db.execute("DELETE FROM token_accounts")
    
    async with httpx.AsyncClient() as http_client:
        while True:
            payload = {
                "jsonrpc": "2.0",
                "method": "getTokenAccounts", 
                "id": "123",
                "params": {
                    "page": page,
                    "limit": 1000,
                    "displayOptions": {},
                    "mint": TOKEN_ADDRESS
                }
            }
            
            try:
                response = await http_client.post(
                    helius_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if not response.is_success:
                    logger.error(f"Helius API error: {response.status_code}")
                    break
                    
                data = response.json()
                
                if not data.get('result') or not data['result'].get('token_accounts'):
                    logger.info(f"No more results. Total pages: {page - 1}")
                    break
                    
                logger.info(f"Processing page {page} with {len(data['result']['token_accounts'])} accounts")
                
                # Batch insert accounts
                accounts_data = []
                for account in data['result']['token_accounts']:
                    wallet_address = account['owner']
                    token_account_address = account['address']
                    balance = int(account['amount'])
                    
                    accounts_data.append((wallet_address, token_account_address, balance))
                
                if accounts_data:
                    async with db.cursor() as acur:
                        await acur.executemany("""
                            INSERT INTO token_accounts (wallet_address, token_account_address, balance, updated_at)
                            VALUES (%s, %s, %s, NOW())
                            ON CONFLICT (wallet_address) DO UPDATE SET
                                token_account_address = EXCLUDED.token_account_address,
                                balance = EXCLUDED.balance,
                                updated_at = NOW()
                        """, accounts_data)
                
                page += 1
                await asyncio.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
    
    await db.commit()
    logger.info(f"Completed token accounts sync from {page-1} pages")
