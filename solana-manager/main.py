import asyncio
import logging
import os
from job_runner import <PERSON><PERSON><PERSON><PERSON>
from solana_jobs import process_payouts, process_burns, fetch_user_accounts, store_pubkey

logging.basicConfig(level=logging.INFO)

async def main():
    db_url = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/postgres')
    
    runner = JobRunner(db_url)

    # On startup, store our pubkey first
    db = await runner.get_db()
    await store_pubkey(db)
    await db.close()

    # Create file for healthcheck
    with open('/tmp/healthy', 'w'):
        pass

    # Register job handlers
    runner.register_handler('payouts', process_payouts)
    runner.register_handler('burns', process_burns) 
    runner.register_handler('accounts', fetch_user_accounts)
    
    # Schedule periodic jobs
    while True:       
        runner.schedule_job('accounts', 'accounts')
        await asyncio.sleep(5)
        #runner.schedule_job('burns', 'burns')
        #await asyncio.sleep(30)

        #runner.schedule_job('payouts', 'payouts')
        #await asyncio.sleep(50)
        
    
    

if __name__ == "__main__":
    asyncio.run(main())