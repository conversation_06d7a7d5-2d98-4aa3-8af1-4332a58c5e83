import asyncio
import logging
import time
from asyncio import sleep
from solana.exceptions import SolanaRpcException
from solders.keypair import Keypair
from solana.rpc.async_api import AsyncClient as Client
import base58
from solana.rpc.commitment import Processed, Commitment, Confirmed
from solana.rpc.core import <PERSON><PERSON><PERSON><PERSON><PERSON>, RPCNoResultException, TransactionExpiredBlockheightExceededError
from solana.rpc.types import TokenAccountOpts, TxOpts
from solders.rpc.responses import SendTransactionResp, GetSignatureStatusesResp, GetLatestBlockhashResp
from solders.signature import Signature

from solders.transaction import VersionedTransaction
from solders.transaction_status import TransactionConfirmationStatus
from solders.message import MessageV0
from solders.compute_budget import set_compute_unit_price, set_compute_unit_limit
import base58
import httpx
import json


async def send_versioned_transaction(
        client: Client, tx: VersionedTransaction, backoff: int = 1) -> SendTransactionResp:
    logging.info(f"Sending transaction {tx.signatures[0]} to the Solana network.")
    try:
        opts = TxOpts(skip_preflight=False, preflight_commitment=Confirmed)
        response = await client.send_raw_transaction(bytes(tx), opts=opts)
    except Exception as E:
        if backoff < 64:
            if isinstance(E, (RPCException, SolanaRpcException, RPCNoResultException)):
                raise SlippageError

            logging.warning(f"Network thrown while trying to send transaction to Solana, "
                            f"backing off for {backoff} seconds")
            await sleep(backoff)
            return await send_versioned_transaction(client, tx, backoff * 2)

        else:
            logging.error(f"{E} thrown while trying to send transaction to Solana, max backoff reached")
            raise E

    return response

async def confirm_transaction(
        client: Client,
        last_block_height: int,
        signature: Signature,
        sleep_seconds: float = 0.4,
        max_timeout: float = 36.0,
) -> bool:
    """
        Wait until the transaction is confirmed. Use this function instead of the one provided by the Solana client,
        to prevent blocking the event loop.

        Args:
            client (AsyncClient): The asynchronous Solana client.
            last_block_height (int): The block height at the time of the transaction.
            signature (str): The transaction signature to confirm.
            sleep_seconds (float): Time to sleep between status checks.
            max_timeout (float): Maximum time to wait for confirmation.

        Returns:
            bool: True if the transaction is confirmed.

        Raises:
            TimeoutError: If the transaction is not confirmed within the maximum timeout.
        """

    start_time = time.time()
    while True:
        # Sleep before checking the status, hopefully the transaction will be confirmed by then :D
        logging.info(f"The transaction {signature} is not confirmed yet, sleeping for {sleep_seconds} seconds.")
        await asyncio.sleep(sleep_seconds)

        response = await client.get_signature_statuses([signature])

        # The transaction has been picked up in this case.
        resp_value = response.value[0]
        if resp_value:
            confirmation_status = resp_value.confirmation_status
            # finalized -> confirmed -> processed, we are fine with just confirmation(1).
            if confirmation_status:
                if int(confirmation_status) >= 1:
                    return True

            # if the transaction has an error, we can return False.
            #  for example, slippage error.
            if resp_value.err:
                logging.warning(f"Transaction {signature} got error: {resp_value.err}")
                return False

        # Either we haven't been picked up yet, or we've been dropped.
        else:
            current_block_height = await client.get_block_height(commitment=Commitment('confirmed'))
            current_block_height = current_block_height.value

            # In case the transaction was dropped, we can return False.
            if current_block_height > last_block_height:
                logging.info(f"Height is now {current_block_height}, tx was put at {last_block_height}. Failed.")
                return False

        # If the transaction is not confirmed within the maximum timeout, raise an error.
        if time.time() - start_time > max_timeout:

            # Check if the transaction was dropped by comparing the current block height with the tx last block height.
            current_block_height = await client.get_block_height(commitment=Commitment('confirmed'))
            if current_block_height.value < last_block_height:
                logging.warning(f"SOL seems congested, height is now {current_block_height.value}, "
                                f"tx was put at {last_block_height}. Waiting longer.")
                # We can safely wait until the block height is greater than the last block height.
                return await confirm_transaction(client, last_block_height, signature, sleep_seconds, max_timeout)

            logging.error(f"The transaction {signature} was not confirmed within the maximum timeout.")
            return False


class SlippageError(Exception):
    pass


async def get_priority_fee_estimate(
    rpc_url: str, 
    serialized_transaction: str, 
    priority_level: str = "Medium"
) -> int:
    """
    Get priority fee estimate from Helius using serialized transaction
    
    Args:
        rpc_url: The Helius RPC endpoint URL
        serialized_transaction: Base58 encoded serialized transaction
        priority_level: Priority level ("Low", "Medium", "High", "VeryHigh")
    
    Returns:
        Priority fee in micro-lamports
    """
    payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getPriorityFeeEstimate",
        "params": [{
            "transaction": serialized_transaction,
            "options": {
                "priorityLevel": priority_level            }
        }]
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            rpc_url,
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if not response.is_success:
            raise Exception(f"RPC request failed: {response.status_code}")
        
        result = response.json()
        
        if result.get("error"):
            raise Exception(f"Fee estimation failed: {json.dumps(result['error'])}")
        
        return result["result"]["priorityFeeEstimate"]


async def build_transaction_with_priority_fee(
    client: Client,
    payer: Keypair,
    instructions: list,
    priority_level: str = "Medium",
    compute_unit_limit: int = 200000
) -> VersionedTransaction:
    """
    Build a transaction with dynamic priority fee estimation
    
    Args:
        client: Solana RPC client
        payer: Transaction payer keypair
        instructions: List of instructions (without priority fee instructions)
        priority_level: Priority level for fee estimation
        compute_unit_limit: Compute unit limit
        
    Returns:
        VersionedTransaction ready to send
    """
    # 1. Get latest blockhash
    blockhash_response = await client.get_latest_blockhash()
    recent_blockhash = blockhash_response.value.blockhash
    
    # 2. Build transaction with placeholder priority fee for serialization
    temp_instructions = [
        set_compute_unit_limit(compute_unit_limit),
        set_compute_unit_price(1000),  # Placeholder price
        *instructions
    ]
    
    temp_message = MessageV0.try_compile(
        payer=payer.pubkey(),
        instructions=temp_instructions,
        address_lookup_table_accounts=[],
        recent_blockhash=recent_blockhash
    )
    
    temp_tx = VersionedTransaction(temp_message, [payer])
    
    # 3. Serialize transaction for fee estimation
    serialized_tx = base58.b58encode(bytes(temp_tx)).decode('utf-8')
    
    # 4. Get priority fee estimate 
    rpc_url = str(client._provider.endpoint_uri)
    try:
        priority_fee = await get_priority_fee_estimate(rpc_url, serialized_tx, priority_level)
        priority_fee = int(priority_fee)
        logging.info(f"Estimated priority fee: {priority_fee} micro-lamports")
    except Exception as e:
        logging.warning(f"Priority fee estimation failed: {e}, using fallback")
        fallback_fees = {
            "Low": 1000,
            "Medium": 5000, 
            "High": 15000,
            "VeryHigh": 50000
        }
        priority_fee = fallback_fees.get(priority_level, 5000)
    
    # 5. Rebuild transaction with actual priority fee
    final_instructions = [
        set_compute_unit_limit(compute_unit_limit),
        set_compute_unit_price(priority_fee),
        *instructions
    ]
    
    final_message = MessageV0.try_compile(
        payer=payer.pubkey(),
        instructions=final_instructions,
        address_lookup_table_accounts=[],
        recent_blockhash=recent_blockhash
    )
    
    final_tx = VersionedTransaction(final_message, [payer])
    
    return final_tx


async def send_and_confirm_transaction_with_priority_fee(
    client: Client,
    payer: Keypair,
    instructions: list,
    priority_level: str = "Medium",
    compute_unit_limit: int = 200000,
    max_retries: int = 3
) -> str:
    """
    Complete workflow: build transaction with priority fee, send, and confirm
    
    Args:
        client: Solana RPC client
        payer: Transaction payer keypair  
        instructions: List of instructions (without priority fee instructions)
        priority_level: Priority level for fee estimation
        compute_unit_limit: Compute unit limit
        max_retries: Maximum retry attempts
        
    Returns:
        Transaction signature
    """
    for attempt in range(max_retries):
        try:
            # Build transaction with priority fee
            tx = await build_transaction_with_priority_fee(
                client, payer, instructions, priority_level, compute_unit_limit
            )
            
            # Get block height for confirmation
            block_height_response = await client.get_latest_blockhash()
            last_block_height = block_height_response.value.last_valid_block_height
            
            # Send transaction
            response = await send_versioned_transaction(client, tx)
            signature = response.value
            
            if not signature:
                raise Exception("Transaction failed to send")
                
            # Confirm transaction
            confirmed = await confirm_transaction(client, last_block_height, signature)
            
            if confirmed:
                logging.info(f"Transaction confirmed: {signature}")
                return str(signature)
            else:
                raise Exception(f"Transaction not confirmed: {signature}")
                
        except SlippageError:
            if attempt < max_retries - 1:
                logging.warning(f"Slippage error, retrying attempt {attempt + 2}/{max_retries}")
                await asyncio.sleep(1)
                continue
            raise
        except Exception as e:
            if attempt < max_retries - 1:
                logging.warning(f"Transaction attempt {attempt + 1} failed: {e}, retrying...")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                continue
            raise
    
    raise Exception(f"Transaction failed after {max_retries} attempts")
