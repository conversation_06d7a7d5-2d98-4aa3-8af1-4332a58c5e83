import asyncio
import logging
from typing import Set, Callable, Any
from datetime import datetime
import os
import psycopg

logger = logging.getLogger(__name__)

class JobRunner:
    def __init__(self, db_url: str):
        self.db_url = db_url
        self.running_jobs: Set[str] = set()
        self.handlers = {}
        
    def register_handler(self, job_type: str, handler: Callable):
        """Register a job handler function"""
        self.handlers[job_type] = handler
        
    async def get_db(self):
        """Get async database connection"""
        return await psycopg.AsyncConnection.connect(self.db_url)
        
    async def run_job(self, job_id: str, job_type: str):
        """Run a single job if not already running"""
        if job_id in self.running_jobs:
            return
            
        if job_type not in self.handlers:
            logger.error(f"No handler for job type: {job_type}")
            return
            
        self.running_jobs.add(job_id)
        try:
            db = await self.get_db()
            await self.handlers[job_type](db, job_id)
            await db.close()
            logger.info(f"Completed job {job_id}")
        except KeyboardInterrupt as e:
            logger.error(f"Job {job_id} failed: {e}")
        finally:
            self.running_jobs.discard(job_id)
    def schedule_job(self, job_id: str, job_type: str):
        """Schedule a job to run"""
        asyncio.create_task(self.run_job(job_id, job_type))