FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install Python dependencies
COPY pumpfun-data/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the websocket service
COPY pumpfun-data/websocket_service.py .

# Set environment variables
ENV ROOM_ID=EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
ENV PUMP_WS=wss://pump.fun
ENV DATABASE_URL=**************************************/postgres

CMD ["python", "websocket_service.py"]