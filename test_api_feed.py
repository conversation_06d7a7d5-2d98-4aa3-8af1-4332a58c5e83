import requests
import random
import time

API = 'http://localhost:8000/chat/'

possible_msgs = ['hit', 'stand', 'join']
random_users = [f'yotsuba{i}' for i in range(25)]

def send_msg(user, msg):
    data = {
        'username': user,
        'message': msg
    }
    response = requests.post(API, json=data)
    if response.ok:
        print(f"Sent message from {user}: {msg}")
    else:
        print(f"Failed to send message from {user}: {msg}")

def send_random_msgs():
    for user in random_users:
        msg = possible_msgs[random.randint(0, 2)]
        send_msg(user, msg)

while True:
    send_random_msgs()
    time.sleep(0.5)
