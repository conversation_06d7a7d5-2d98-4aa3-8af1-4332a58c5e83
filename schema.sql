-- ==============================
-- Blackjack Plays Database Schema
-- ==============================

-- USERS: Players/viewers that participate
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username TEXT NOT NULL,
    wallet_address TEXT, -- optional Solana or other identifier
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- BLACKJACK GAMES: Stored only after game completes
CREATE TABLE blackjack_games (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMP DEFAULT now(),
    ended_at TIMESTAMP DEFAULT now(),
    seed TEXT, -- provable fairness seed if desired
    token_pool NUMERIC(78, 0) DEFAULT 0 -- token pool for this game
);

-- BLACKJACK EVENTS: Actual applied moves (no per-user)
CREATE TABLE blackjack_events (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    action TEXT CHECK (
        action IN (
            'deal',
            'hit',
            'stand',
            'double',
            'split',
            'end'
        )
    ) NOT NULL,
    card JSONB, -- {"rank":"K","suit":"hearts"} or null
    created_at TIMESTAMP DEFAULT now()
);

-- BLACKJACK VOTES: Stores all individual user decisions
CREATE TABLE blackjack_votes (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    mode TEXT CHECK (
        mode IN ('anarchy', 'democracy')
    ) NOT NULL,
    move TEXT CHECK (
        move IN (
            'hit',
            'stand',
            'double',
            'split'
        )
    ) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- USER VIEWING DATA: tracks if a user was present at a timestamp
CREATE TABLE user_views (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    viewed_at TIMESTAMP DEFAULT now()
);

-- SOLANA CONFIG: manages game payout rules and balances
CREATE TABLE solana_config (
    id BIGSERIAL PRIMARY KEY,
    token_treshold_amount NUMERIC(78, 0) DEFAULT 1000000 -- 1 token, -- 30000000000000, -- 30M tokens / 3% of supply stored
    game_stake_percent NUMERIC(5, 2) DEFAULT 0.10, -- % of balance used per game
    burn_percent NUMERIC(5, 2) DEFAULT 35.00, -- % burnt when house wins
    keep_percent NUMERIC(5, 2) DEFAULT 10.00, -- % kept when house wins
    next_stake_percent NUMERIC(5, 2) DEFAULT 55.00, -- % added to next stake when house wins
    public_key TEXT, -- public key for writing to
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- USER WINNERS: which users won and how much
CREATE TABLE user_winners (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    payout NUMERIC(78, 0) NOT NULL,
    tx_hash TEXT,
    result TEXT CHECK (
        result IN (
            'dealer_busted',
            'player_busted',
            'dealer_win',
            'player_win',
            'push'
        )
    ) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- Token burns - store any burns we need to do after blackjack games here
CREATE TABLE token_burns (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    amount NUMERIC(78, 0) NOT NULL,
    tx_hash TEXT,
    created_at TIMESTAMP DEFAULT now()
);

-- User token accounts & balances (from solana manager)
CREATE TABLE token_accounts (
    wallet_address TEXT PRIMARY KEY,
    token_account_address TEXT NOT NULL,
    balance NUMERIC(78, 0) DEFAULT 0, -- token has 6 decimals. for both we store uint256 which is effectively 78 digits
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- INDEXES for speed
CREATE INDEX idx_events_game_id ON blackjack_events (game_id);

CREATE INDEX idx_votes_game_id ON blackjack_votes (game_id);

CREATE INDEX idx_views_user_id ON user_views (user_id);

CREATE INDEX idx_winners_user_id ON user_winners (user_id);

CREATE INDEX idx_token_accounts_wallet_address ON token_accounts (wallet_address);

CREATE INDEX idx_burns_game_id ON token_burns (game_id);

CREATE INDEX idx_burns_tx_hash ON token_burns (tx_hash);

CREATE INDEX idx_votes_user_id_timestamp ON blackjack_votes (user_id, created_at);

CREATE INDEX idx_views_user_id_timestamp ON user_views (user_id, viewed_at);

-- Unique usernames in users
CREATE UNIQUE INDEX idx_users_username ON users (username);

-- Insert base config

INSERT INTO solana_config (id) VALUES (1);